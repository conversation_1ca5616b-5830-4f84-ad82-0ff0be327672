<template>
  <div class="submit-page">
    <div class="container-fluid">
      <!-- Sample Detail Info Section -->
      <div class="card mb-3">
        <h3 class="section-title">Sample Detail Info</h3>
        <el-divider class="mt-1"></el-divider>

        <!-- Loading State -->
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="8" animated />
        </div>

        <!-- Sample Details -->
        <el-descriptions v-else :column="2" border class="sample-descriptions">
          <el-descriptions-item label="Run ID">
            {{ sampleDetail.runId || '-' }}
          </el-descriptions-item>

          <el-descriptions-item label="BioProject ID">
            {{ sampleDetail.projectId || '-' }}
          </el-descriptions-item>

          <el-descriptions-item label="Longitude">
            {{ sampleDetail.longitude || '-' }}
          </el-descriptions-item>

          <el-descriptions-item label="Latitude">
            {{ sampleDetail.latitude || '-' }}
          </el-descriptions-item>

          <el-descriptions-item label="Depth (m)">
            {{ sampleDetail.depth || '-' }}
          </el-descriptions-item>

          <el-descriptions-item label="Temperature">
            {{ sampleDetail.temperature || '-' }}
          </el-descriptions-item>

          <el-descriptions-item label="Salinity">
            {{ sampleDetail.salinity || '-' }}
          </el-descriptions-item>

          <el-descriptions-item label="pH">
            {{ sampleDetail.ph || '-' }}
          </el-descriptions-item>

          <el-descriptions-item label="Hydrosphere Type">
            {{ sampleDetail.hydrosphereType || '-' }}
          </el-descriptions-item>

          <el-descriptions-item label="Water Body Type">
            {{ sampleDetail.waterBodyType || '-' }}
          </el-descriptions-item>

          <el-descriptions-item label="Geolocation">
            {{ sampleDetail.geolocation || '-' }}
          </el-descriptions-item>

          <el-descriptions-item label="Critical Zone">
            {{ sampleDetail.criticalZone || '-' }}
          </el-descriptions-item>

          <el-descriptions-item label="Sampling Substrate">
            {{ sampleDetail.samplingSubstrate || '-' }}
          </el-descriptions-item>

          <el-descriptions-item label="Country">
            {{ sampleDetail.country || '-' }}
          </el-descriptions-item>

          <el-descriptions-item label="Water Body Name">
            {{ sampleDetail.waterBodyName || '-' }}
          </el-descriptions-item>

          <el-descriptions-item label="Center Name">
            {{ sampleDetail.centerName || '-' }}
          </el-descriptions-item>

          <el-descriptions-item label="Instrument">
            {{ sampleDetail.instrument || '-' }}
          </el-descriptions-item>

          <el-descriptions-item label="SeqNum Assembled">
            {{ sampleDetail.seqNumAssembled || '-' }}
          </el-descriptions-item>

          <el-descriptions-item label="SeqBase Assembled (bp)">
            {{ sampleDetail.seqBaseAssembled || '-' }}
          </el-descriptions-item>

          <el-descriptions-item label="SeqNum Reads">
            {{ sampleDetail.seqNumReads || '-' }}
          </el-descriptions-item>

          <el-descriptions-item label="SeqBase Reads1 (bp)">
            {{ sampleDetail.seqBaseReads1 || '-' }}
          </el-descriptions-item>

          <el-descriptions-item label="SeqBase Reads2 (bp)">
            {{ sampleDetail.seqBaseReads2 || '-' }}
          </el-descriptions-item>

          <el-descriptions-item label="Study Name" span="2">
            {{ sampleDetail.studyName || '-' }}
          </el-descriptions-item>

          <el-descriptions-item label="Rawdata Link" span="2">
            <a
              v-if="sampleDetail.rawdataLink"
              :href="sampleDetail.rawdataLink"
              target="_blank"
              class="link-style"
            >
              {{ sampleDetail.rawdataLink }}
            </a>
            <span v-else>-</span>
          </el-descriptions-item>

          <el-descriptions-item label="Study Abstract" span="2">
            <div class="abstract-text">{{ sampleDetail.studyAbstract || '-' }}</div>
          </el-descriptions-item>

          <el-descriptions-item label="Taxonomy" span="2" v-if="sampleDetail.taxonomyUrl">
            <div class="taxonomy-iframe-container">
              <iframe
                :src="sampleDetail.taxonomyUrl"
                class="taxonomy-iframe"
              ></iframe>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- Analysis Results Table Section -->
      <div class="card">
        <h3 class="section-title">Analysis Results</h3>
        <el-divider class="mt-1"></el-divider>

        <el-table
          :data="analysisData"
          style="width: 100%"
          border
          :header-cell-style="{
            backgroundColor: '#F1F5F9',
            color: '#333333',
            fontWeight: 700,
          }"
        >
          <el-table-column label="KO" prop="ko" width="120">
            <template #default="scope">
              <a
                :href="`https://www.genome.jp/entry/${scope.row.ko}`"
                target="_blank"
                class="link-style"
              >
                {{ scope.row.ko }}
              </a>
            </template>
          </el-table-column>

          <el-table-column
            label="Depth"
            prop="depth"
            width="120"
          ></el-table-column>

          <el-table-column label="Pathway" prop="pathway" min-width="200">
            <template #default="scope">
              <div class="pathway-container">
                <span
                  v-for="(path, index) in scope.row.pathwayList"
                  :key="index"
                  class="pathway-item"
                >
                  <a
                    :href="`https://www.kegg.jp/entry/${path}`"
                    target="_blank"
                    class="link-style"
                  >
                    {{ path }}
                  </a>
                  <span v-if="index < scope.row.pathwayList.length - 1"
                    >;
                  </span>
                </span>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          class="mb-1 mt-2 justify-center"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          :total="analysisData.length"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
  import { useRoute } from 'vue-router';
  import { getSampleByRunId } from '@/api/samples';

  const { proxy } = getCurrentInstance();
  const route = useRoute();

  // Pagination
  const currentPage = ref(1);
  const pageSize = ref(10);

  // Loading state
  const loading = ref(false);

  // Sample Detail Information
  const sampleDetail = reactive({
    runId: '',
    projectId: '',
    longitude: '',
    latitude: '',
    depth: '',
    temperature: '',
    salinity: '',
    ph: '',
    hydrosphereType: '',
    waterBodyType: '',
    geolocation: '',
    criticalZone: '',
    samplingSubstrate: '',
    country: '',
    waterBodyName: '',
    centerName: '',
    instrument: '',
    seqNumAssembled: '',
    seqBaseAssembled: '',
    seqNumReads: '',
    seqBaseReads1: '',
    seqBaseReads2: '',
    studyName: '',
    rawdataLink: '',
    studyAbstract: '',
    taxonomyUrl: '',
  });

  // Analysis Results Data
  const analysisData = reactive([
    {
      ko: 'K00001',
      depth: '8.5085',
      pathway: 'map00010;map00011',
      pathwayList: ['map00010', 'map00011'],
    },
    {
      ko: 'K00002',
      depth: '7.2341',
      pathway: 'map00020;map00030;map00040',
      pathwayList: ['map00020', 'map00030', 'map00040'],
    },
    {
      ko: 'K00003',
      depth: '9.1256',
      pathway: 'map00050',
      pathwayList: ['map00050'],
    },
    {
      ko: 'K00004',
      depth: '6.7892',
      pathway: 'map00060;map00070',
      pathwayList: ['map00060', 'map00070'],
    },
    {
      ko: 'K00005',
      depth: '8.9123',
      pathway: 'map00080;map00090;map00100;map00110',
      pathwayList: ['map00080', 'map00090', 'map00100', 'map00110'],
    },
    {
      ko: 'K00006',
      depth: '5.4567',
      pathway: 'map00120',
      pathwayList: ['map00120'],
    },
    {
      ko: 'K00007',
      depth: '7.8901',
      pathway: 'map00130;map00140',
      pathwayList: ['map00130', 'map00140'],
    },
    {
      ko: 'K00008',
      depth: '9.3456',
      pathway: 'map00150;map00160;map00170',
      pathwayList: ['map00150', 'map00160', 'map00170'],
    },
    {
      ko: 'K00009',
      depth: '6.2345',
      pathway: 'map00180',
      pathwayList: ['map00180'],
    },
    {
      ko: 'K00010',
      depth: '8.1234',
      pathway: 'map00190;map00200',
      pathwayList: ['map00190', 'map00200'],
    },
    {
      ko: 'K00011',
      depth: '7.5678',
      pathway: 'map00210;map00220;map00230',
      pathwayList: ['map00210', 'map00220', 'map00230'],
    },
    {
      ko: 'K00012',
      depth: '9.8765',
      pathway: 'map00240',
      pathwayList: ['map00240'],
    },
    {
      ko: 'K00013',
      depth: '5.9876',
      pathway: 'map00250;map00260',
      pathwayList: ['map00250', 'map00260'],
    },
    {
      ko: 'K00014',
      depth: '8.4321',
      pathway: 'map00270;map00280;map00290;map00300',
      pathwayList: ['map00270', 'map00280', 'map00290', 'map00300'],
    },
    {
      ko: 'K00015',
      depth: '6.7654',
      pathway: 'map00310',
      pathwayList: ['map00310'],
    },
  ]);

  // 获取样本详情
  async function fetchSampleDetail() {
    const runId = route.params.id;
    if (!runId) {
      proxy.$modal.msgError('Invalid sample ID');
      return;
    }

    try {
      loading.value = true;
      const response = await getSampleByRunId(runId);

      if (response.data) {
        // 更新样本详情数据
        Object.assign(sampleDetail, response.data);
      }
    } catch (error) {
      console.error('Failed to fetch sample detail:', error);
      proxy.$modal.msgError('Failed to load sample details');
    } finally {
      loading.value = false;
    }
  }

  // 页面初始化
  onMounted(() => {
    fetchSampleDetail();
  });
</script>

<style lang="scss" scoped>
  .container-fluid {
    max-width: 1640px !important;
  }

  .submit-page {
    padding: 140px 0 45px 0;
  }

  .section-title {
    display: flex;
    align-items: center;
    color: #1e7cb2;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 0;
  }

  .filter {
    width: 24px;
    height: 26px;
    margin-right: 0.5rem;
  }

  .sample-descriptions {
    margin: 16px;

    :deep(.el-descriptions__label) {
      background-color: #f1f5f9 !important;
      font-weight: 600;
      color: #333333;
      padding: 12px 16px;
    }

    :deep(.el-descriptions__content) {
      color: #666666;
      line-height: 1.5;
      word-break: break-word;
      padding: 12px 16px;
    }

    // Full width items (span=2)
    :deep(.el-descriptions-item__cell[colspan='2']) {
      .el-descriptions__content {
        width: calc(100% - 200px) !important;
      }
    }

    :deep(.el-descriptions__table) {
      border: 1px solid #e4e7ed;
    }

    :deep(.el-descriptions-item__container) {
      border-bottom: 1px solid #e4e7ed;

      &:last-child {
        border-bottom: none;
      }
    }
  }

  .abstract-text {
    text-align: justify;
    line-height: 1.6;
  }

  .taxonomy-iframe {
    width: 100%;
    height: 600px;
    border: none;
    display: block;
    background-color: #ffffff;
  }

  .link-style {
    color: #3498db;
    text-decoration: none;
    transition: all 0.3s ease;

    &:hover {
      color: #2980b9;
      text-decoration: underline;
    }

    //&:visited {
    //  color: #8e44ad;
    //}
  }

  .pathway-container {
    line-height: 1.5;
  }

  .pathway-item {
    display: inline;

    .link-style {
      margin-right: 2px;
    }
  }

  .mb-3 {
    margin-bottom: 24px;
  }

  .loading-container {
    padding: 20px;
  }

  :deep(.el-table) {
    font-size: 14px;
  }

  :deep(.el-table .cell) {
    padding: 8px 12px;
    line-height: 1.5;
  }

  :deep(.el-table__header .cell) {
    padding: 12px;
    font-weight: 600;
  }

  :deep(.el-divider) {
    margin: 16px 0;
  }

  :deep(.el-pagination) {
    margin-top: 20px;
  }

  // Responsive design
  @media (max-width: 768px) {
    .sample-descriptions {
      margin: 8px;

      :deep(.el-descriptions__label) {
        width: 150px;
        padding: 8px 12px;
        font-size: 14px;
      }

      :deep(.el-descriptions__content) {
        padding: 8px 12px;
        font-size: 14px;
      }
    }

    .taxonomy-iframe-container {
      height: 400px;
    }

    .sample-descriptions {
      :deep(.el-descriptions__content) {
        width: calc(100% - 150px) !important;
      }
    }
  }
</style>
